import browser from "webextension-polyfill";

type Message = {
  action: 'fetch' | 'startPromptProcessing' | 'promptProcessed' | 'processingComplete',
  value?: any,
  prompts?: string[],
  currentIndex?: number,
  error?: string
}

type ResponseCallback = (data: any) => void

async function handleMessage({action, value, prompts, currentIndex, error}: Message, response: ResponseCallback) {
  if (action === 'fetch') {
    const result = await fetch('https://meowfacts.herokuapp.com/');
    const { data } = await result.json();
    response({ message: 'success', data });
  }
  else if (action === 'startPromptProcessing') {
    try {
      // Find the Runware AI tab
      const tabs = await browser.tabs.query({ url: '*://my.runware.ai/*' });

      if (tabs.length === 0) {
        response({ error: 'No Runware AI tab found' });
        return;
      }

      const tab = tabs[0];
      if (!tab.id) {
        response({ error: 'Invalid tab ID' });
        return;
      }

      // Send message to content script
      await browser.tabs.sendMessage(tab.id, {
        action: 'processPrompts',
        prompts: prompts
      });

      response({ message: 'Processing started' });
    } catch (err) {
      console.error('Error starting prompt processing:', err);
      response({ error: 'Failed to start processing' });
    }
  }
  else if (action === 'promptProcessed') {
    // Forward progress update to popup if needed
    response({ message: 'Progress updated', currentIndex });
  }
  else if (action === 'processingComplete') {
    response({ message: 'Processing completed' });
  }
  else {
    response({data: null, error: 'Unknown action'});
  }
}

// @ts-ignore
browser.runtime.onMessage.addListener((msg, sender, response) => {
  handleMessage(msg, response);
  return true;
});
