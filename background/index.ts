import browser from "webextension-polyfill";

type Message = {
  action: 'startPromptProcessing' | 'promptProcessed' | 'processingComplete',
  value?: any,
  prompts?: string[],
  currentIndex?: number,
  error?: string,
  delaySeconds?: number
}

type ResponseCallback = (data: any) => void

async function handleMessage({action, prompts, currentIndex, delaySeconds}: Message, response: ResponseCallback) {
  if (action === 'startPromptProcessing') {
    console.log('🎯 Background: Received startPromptProcessing message');
    console.log('📝 Prompts:', prompts);
    console.log('⏱️ Delay:', delaySeconds);

    try {
      // Find the Runware AI tab
      const tabs = await browser.tabs.query({ url: '*://my.runware.ai/*' });
      console.log('🔍 Found tabs:', tabs.length);

      if (tabs.length === 0) {
        console.log('❌ No Runware tab found, opening one...');
        // Open Runware tab if not found
        const newTab = await browser.tabs.create({
          url: 'https://my.runware.ai/playground?modelAIR=runware%3A101%401&modelArchitecture=flux1d'
        });

        // Wait a bit for the tab to load
        setTimeout(async () => {
          try {
            await browser.tabs.sendMessage(newTab.id!, {
              action: 'processPrompts',
              prompts: prompts,
              delaySeconds: delaySeconds || 3
            });
            console.log('✅ Message sent to new tab');
          } catch (err) {
            console.error('❌ Error sending to new tab:', err);
          }
        }, 3000);

        response({ message: 'Runware tab opened, processing will start in 3 seconds...' });
        return;
      }

      const tab = tabs[0];
      if (!tab.id) {
        response({ error: 'Invalid tab ID' });
        return;
      }

      console.log('📤 Sending message to content script in tab:', tab.id);
      // Send message to content script
      await browser.tabs.sendMessage(tab.id, {
        action: 'processPrompts',
        prompts: prompts,
        delaySeconds: delaySeconds || 3
      });

      console.log('✅ Processing message sent successfully');
      response({ message: 'Processing started successfully!' });
    } catch (err) {
      console.error('❌ Error starting prompt processing:', err);
      response({ error: `Failed to start processing: ${err.message}` });
    }
  }
  else if (action === 'promptProcessed') {
    // Forward progress update to popup if needed
    response({ message: 'Progress updated', currentIndex });
  }
  else if (action === 'processingComplete') {
    response({ message: 'Processing completed' });
  }
  else {
    response({data: null, error: 'Unknown action'});
  }
}

// @ts-ignore
browser.runtime.onMessage.addListener((msg, _sender, response) => {
  handleMessage(msg, response);
  return true;
});
