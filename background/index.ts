import browser from "webextension-polyfill";

type Message = {
  action: 'startPromptProcessing' | 'promptProcessed' | 'processingComplete',
  value?: any,
  prompts?: string[],
  currentIndex?: number,
  error?: string,
  delaySeconds?: number
}

type ResponseCallback = (data: any) => void

async function handleMessage({action, prompts, currentIndex, delaySeconds}: Message, response: ResponseCallback) {
  if (action === 'startPromptProcessing') {
    try {
      // Find the Runware AI tab
      const tabs = await browser.tabs.query({ url: '*://my.runware.ai/*' });

      if (tabs.length === 0) {
        response({ error: 'No Runware AI tab found' });
        return;
      }

      const tab = tabs[0];
      if (!tab.id) {
        response({ error: 'Invalid tab ID' });
        return;
      }

      // Send message to content script
      await browser.tabs.sendMessage(tab.id, {
        action: 'processPrompts',
        prompts: prompts,
        delaySeconds: delaySeconds || 3
      });

      response({ message: 'Processing started' });
    } catch (err) {
      console.error('Error starting prompt processing:', err);
      response({ error: 'Failed to start processing' });
    }
  }
  else if (action === 'promptProcessed') {
    // Forward progress update to popup if needed
    response({ message: 'Progress updated', currentIndex });
  }
  else if (action === 'processingComplete') {
    response({ message: 'Processing completed' });
  }
  else {
    response({data: null, error: 'Unknown action'});
  }
}

// @ts-ignore
browser.runtime.onMessage.addListener((msg, _sender, response) => {
  handleMessage(msg, response);
  return true;
});
