import React, { useState, useRef, useEffect } from 'react'
import browser from 'webextension-polyfill'

interface StoredState {
  fileName: string
  uploadedContent: string
  prompts: string[]
  delaySeconds: number
}

export default () => {
  const [uploadedContent, setUploadedContent] = useState('')
  const [fileName, setFileName] = useState('')
  const [prompts, setPrompts] = useState<string[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentPromptIndex, setCurrentPromptIndex] = useState(0)
  const [processStatus, setProcessStatus] = useState('')
  const [delaySeconds, setDelaySeconds] = useState(3)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [showRestoreMessage, setShowRestoreMessage] = useState(false)
  const [pastedContent, setPastedContent] = useState('')

  // Save state to browser storage
  async function saveState() {
    const state: StoredState = {
      fileName,
      uploadedContent,
      prompts,
      delaySeconds
    }
    try {
      setIsSaving(true)
      console.log('Saving state:', state)
      await browser.storage.local.set({ promptProcessorState: state })
      console.log('State saved successfully')
    } catch (error) {
      console.error('Failed to save state:', error)
    } finally {
      setIsSaving(false)
    }
  }

  // Load state from browser storage
  async function loadState() {
    try {
      console.log('Loading state...')
      const result = await browser.storage.local.get('promptProcessorState')
      console.log('Storage result:', result)
      if (result.promptProcessorState) {
        const state: StoredState = result.promptProcessorState
        console.log('Restoring state:', state)
        setFileName(state.fileName || '')
        setUploadedContent(state.uploadedContent || '')
        setPrompts(state.prompts || [])
        setDelaySeconds(state.delaySeconds || 3)
        console.log('State restored successfully')

        // Show restore message if there was content
        if (state.fileName || state.uploadedContent) {
          setShowRestoreMessage(true)
          setTimeout(() => setShowRestoreMessage(false), 3000)
        }
      } else {
        console.log('No saved state found')
      }
    } catch (error) {
      console.error('Failed to load state:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Clear saved state
  async function clearState() {
    try {
      await browser.storage.local.remove('promptProcessorState')
    } catch (error) {
      console.error('Failed to clear state:', error)
    }
  }

  // Load state when component mounts
  useEffect(() => {
    loadState()
  }, [])

  // Save state whenever relevant data changes
  useEffect(() => {
    if (!isLoading && (fileName || uploadedContent || prompts.length > 0)) {
      console.log('Triggering save state due to data change')
      saveState()
    }
  }, [fileName, uploadedContent, prompts, delaySeconds, isLoading])

  // Prevent popup from closing when there's uploaded content
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (uploadedContent && !isProcessing) {
        e.preventDefault()
        e.returnValue = 'You have uploaded content that will be lost. Are you sure you want to close?'
        return e.returnValue
      }
    }

    const handleVisibilityChange = () => {
      if (document.hidden && uploadedContent) {
        console.log('Document becoming hidden, saving state...')
        saveState()
      }
    }

    // Keep popup focused when there's content
    const keepFocus = () => {
      if (uploadedContent) {
        window.focus()
        document.body.focus()
      }
    }

    // Prevent popup from losing focus during file operations
    const handleFocusOut = (e: FocusEvent) => {
      if (uploadedContent && !isProcessing) {
        setTimeout(() => {
          window.focus()
          document.body.focus()
        }, 10)
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('blur', keepFocus)
    document.addEventListener('focusout', handleFocusOut)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('blur', keepFocus)
      document.removeEventListener('focusout', handleFocusOut)
    }
  }, [uploadedContent, isProcessing])

  function parsePrompts(content: string): string[] {
    // Split by double newlines or single newlines, filter out empty strings
    const lines = content.split(/\n+/).map(line => line.trim()).filter(line => line.length > 0)
    return lines
  }





  function handlePasteContent() {
    if (pastedContent.trim()) {
      console.log('Processing pasted content:', pastedContent)

      // Parse prompts from the pasted content
      const parsedPrompts = parsePrompts(pastedContent)
      console.log('Parsed prompts:', parsedPrompts)

      const newState: StoredState = {
        fileName: 'Pasted Content.txt',
        uploadedContent: pastedContent,
        prompts: parsedPrompts,
        delaySeconds: delaySeconds
      }

      console.log('Saving state:', newState)

      // Update React state immediately
      setUploadedContent(pastedContent)
      setFileName('Pasted Content.txt')
      setPrompts(parsedPrompts)
      setPastedContent('')

      // Save to storage
      browser.storage.local.set({ promptProcessorState: newState }).then(() => {
        console.log('Pasted content state saved successfully')
      }).catch(error => {
        console.error('Failed to save pasted content state:', error)
      })
    } else {
      console.log('No content to paste')
    }
  }

  async function resetExtension() {
    setUploadedContent('')
    setFileName('')
    setPrompts([])
    setCurrentPromptIndex(0)
    setProcessStatus('')
    setPastedContent('')
    // Clear saved state
    await clearState()
  }

  async function checkRunwareTab(): Promise<boolean> {
    try {
      const tabs = await browser.tabs.query({ url: '*://my.runware.ai/*' })
      return tabs.length > 0
    } catch (error) {
      console.error('Error checking for Runware tab:', error)
      return false
    }
  }

  async function startProcessingPrompts() {
    if (prompts.length === 0) {
      alert('No prompts found in the uploaded file')
      return
    }

    const hasRunwareTab = await checkRunwareTab()
    if (!hasRunwareTab) {
      const shouldOpen = confirm(
        'Runware AI playground is not open. Would you like to open it now?\n\n' +
        'Click OK to open the tab, then click the Process button again to start processing.'
      )
      if (shouldOpen) {
        await browser.tabs.create({
          url: 'https://my.runware.ai/playground?modelAIR=runware%3A101%401&modelArchitecture=flux1d'
        })
        setProcessStatus('Runware tab opened. Please click Process again to start.')
      }
      return
    }

    setIsProcessing(true)
    setCurrentPromptIndex(0)
    setProcessStatus(`Starting to process ${prompts.length} prompts...`)

    try {
      // Send message to content script to start processing
      await browser.runtime.sendMessage({
        action: 'startPromptProcessing',
        prompts: prompts,
        delaySeconds: delaySeconds
      })
    } catch (error) {
      console.error('Error starting prompt processing:', error)
      setProcessStatus('Error: Failed to start processing')
      setIsProcessing(false)
    }
  }

  useEffect(() => {
    // Listen for processing updates from background script
    const messageListener = (message: any) => {
      if (message.action === 'processingUpdate') {
        setCurrentPromptIndex(message.currentIndex || 0)
        setProcessStatus(message.status || '')
      } else if (message.action === 'processingComplete') {
        setIsProcessing(false)
        setProcessStatus('All prompts processed successfully!')
      } else if (message.action === 'processingError') {
        setIsProcessing(false)
        setProcessStatus(`Error: ${message.error}`)
      }
    }

    // Check if browser runtime is available (not in test environment)
    if (browser?.runtime?.onMessage) {
      browser.runtime.onMessage.addListener(messageListener)

      return () => {
        browser.runtime.onMessage.removeListener(messageListener)
      }
    }
  }, [])

  // Show loading state while restoring data
  if (isLoading) {
    return (
      <div className='w-96 min-h-[500px] bg-white shadow-2xl flex items-center justify-center'>
        <div className='text-center'>
          <div className='w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4'></div>
          <p className='text-gray-600 font-medium'>Restoring your data...</p>
          <p className='text-gray-500 text-sm mt-2'>Checking for previously uploaded files</p>
        </div>
      </div>
    )
  }

  return (
    <div className='w-96 min-h-[500px] bg-white shadow-2xl popup-container' tabIndex={-1}>
      {/* Header */}
      <div className='bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white'>
        <div className='flex items-center gap-3'>
          <div className='w-10 h-10 bg-white/20 rounded-full flex items-center justify-center'>
            <svg className='w-6 h-6' fill='currentColor' viewBox='0 0 20 20'>
              <path d='M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'/>
            </svg>
          </div>
          <div className='flex-1'>
            <h1 className='text-xl font-bold'>AI Prompt Processor</h1>
            <p className='text-blue-100 text-sm'>Batch process your prompts</p>
          </div>
          {isSaving && (
            <div className='flex items-center gap-2 text-blue-100'>
              <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin'></div>
              <span className='text-sm'>Saving...</span>
            </div>
          )}
        </div>
      </div>

      {/* Restore Message */}
      {showRestoreMessage && (
        <div className='bg-green-50 border-l-4 border-green-400 p-4 mx-6 mt-4'>
          <div className='flex items-center'>
            <div className='flex-shrink-0'>
              <svg className='h-5 w-5 text-green-400' viewBox='0 0 20 20' fill='currentColor'>
                <path fillRule='evenodd' d='M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z' clipRule='evenodd' />
              </svg>
            </div>
            <div className='ml-3'>
              <p className='text-sm text-green-700 font-medium'>
                Your previous file has been restored: {fileName}
              </p>
            </div>
          </div>
        </div>
      )}

      <div className='p-6'>
        {!fileName ? (
          /* Paste Only - No File Upload */
          <div className='border-2 border-blue-200 rounded-xl p-6 bg-blue-50'>
            <div className='text-center mb-6'>
              <div className='w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center'>
                <svg className='w-8 h-8 text-blue-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                  <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z' />
                </svg>
              </div>
              <h3 className='text-xl font-semibold text-gray-700 mb-2'>Add Your Prompts</h3>
              <p className='text-gray-600'>Paste your prompts below (one per line)</p>
            </div>

            <textarea
              value={pastedContent}
              onChange={(e) => setPastedContent(e.target.value)}
              placeholder="Paste your prompts here, for example:&#10;A beautiful sunset over mountains&#10;A cat playing with yarn&#10;A futuristic city with flying cars"
              className='w-full h-40 p-4 border-2 border-blue-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-700'
            />

            <button
              onClick={handlePasteContent}
              disabled={!pastedContent.trim()}
              className='w-full mt-4 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-6 py-3 rounded-lg font-medium transition-colors text-lg'
            >
              {pastedContent.trim() ? `Process ${parsePrompts(pastedContent).length} Prompts` : 'Process Prompts'}
            </button>

            <p className='text-sm text-gray-500 mt-3 text-center'>
              Each line will be treated as a separate prompt for processing
            </p>
          </div>
        ) : (
          /* File Uploaded - Processing Area */
          <div className='space-y-6'>
            {/* File Info */}
            <div className='bg-green-50 border border-green-200 rounded-lg p-4'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center gap-3'>
                  <div className='w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center'>
                    <svg className='w-5 h-5 text-green-600' fill='currentColor' viewBox='0 0 20 20'>
                      <path fillRule='evenodd' d='M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z' clipRule='evenodd' />
                    </svg>
                  </div>
                  <div>
                    <h3 className='font-semibold text-green-800'>{fileName}</h3>
                    <p className='text-sm text-green-600'>
                      {prompts.length} prompts • {uploadedContent.length} characters
                    </p>
                  </div>
                </div>
                <button
                  onClick={resetExtension}
                  className='text-green-600 hover:text-green-800 p-1'
                >
                  <svg className='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
                    <path fillRule='evenodd' d='M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z' clipRule='evenodd' />
                  </svg>
                </button>
              </div>
            </div>

            {/* Preview */}
            <div className='bg-gray-50 rounded-lg p-4'>
              <h4 className='font-medium text-gray-700 mb-2'>Preview</h4>
              <div className='max-h-32 overflow-y-auto bg-white border rounded p-3 text-sm text-gray-600'>
                <pre className='whitespace-pre-wrap break-words font-mono'>
                  {uploadedContent.length > 300
                    ? uploadedContent.substring(0, 300) + '...'
                    : uploadedContent
                  }
                </pre>
              </div>
            </div>

            {/* Settings */}
            <div className='bg-gray-50 rounded-lg p-4'>
              <h4 className='font-medium text-gray-700 mb-3'>Processing Settings</h4>
              <div>
                <label className='block text-sm font-medium text-gray-600 mb-2'>
                  Delay between prompts (seconds)
                </label>
                <input
                  type="number"
                  min="1"
                  max="60"
                  value={delaySeconds}
                  onChange={(e) => setDelaySeconds(parseInt(e.target.value) || 3)}
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                />
                <p className='text-xs text-gray-500 mt-1'>
                  Recommended: 3-5 seconds to avoid rate limiting
                </p>
              </div>
            </div>

            {/* Process Button */}
            <button
              onClick={startProcessingPrompts}
              disabled={isProcessing}
              className={`w-full py-3 px-4 rounded-lg font-semibold text-white transition-all duration-200 ${
                isProcessing
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl'
              }`}
            >
              {isProcessing ? (
                <div className='flex items-center justify-center gap-2'>
                  <svg className='animate-spin w-5 h-5' fill='none' viewBox='0 0 24 24'>
                    <circle className='opacity-25' cx='12' cy='12' r='10' stroke='currentColor' strokeWidth='4'></circle>
                    <path className='opacity-75' fill='currentColor' d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'></path>
                  </svg>
                  Processing...
                </div>
              ) : (
                `🚀 Process ${prompts.length} Prompts`
              )}
            </button>

            {/* Status */}
            {processStatus && (
              <div className={`rounded-lg p-4 ${
                isProcessing
                  ? 'bg-blue-50 border border-blue-200'
                  : processStatus.includes('Error')
                    ? 'bg-red-50 border border-red-200'
                    : 'bg-green-50 border border-green-200'
              }`}>
                <div className='flex items-start gap-3'>
                  <div className={`w-5 h-5 mt-0.5 ${
                    isProcessing
                      ? 'text-blue-600'
                      : processStatus.includes('Error')
                        ? 'text-red-600'
                        : 'text-green-600'
                  }`}>
                    {isProcessing ? (
                      <svg className='animate-spin w-5 h-5' fill='none' viewBox='0 0 24 24'>
                        <circle className='opacity-25' cx='12' cy='12' r='10' stroke='currentColor' strokeWidth='4'></circle>
                        <path className='opacity-75' fill='currentColor' d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'></path>
                      </svg>
                    ) : processStatus.includes('Error') ? (
                      <svg fill='currentColor' viewBox='0 0 20 20'>
                        <path fillRule='evenodd' d='M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z' clipRule='evenodd' />
                      </svg>
                    ) : (
                      <svg fill='currentColor' viewBox='0 0 20 20'>
                        <path fillRule='evenodd' d='M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z' clipRule='evenodd' />
                      </svg>
                    )}
                  </div>
                  <div className='flex-1'>
                    <p className={`text-sm font-medium ${
                      isProcessing
                        ? 'text-blue-800'
                        : processStatus.includes('Error')
                          ? 'text-red-800'
                          : 'text-green-800'
                    }`}>
                      {processStatus}
                    </p>
                    {isProcessing && (
                      <div className='mt-2'>
                        <div className='flex justify-between text-xs text-blue-600 mb-1'>
                          <span>Progress</span>
                          <span>{currentPromptIndex + 1} / {prompts.length}</span>
                        </div>
                        <div className='w-full bg-blue-200 rounded-full h-2'>
                          <div
                            className='bg-blue-600 h-2 rounded-full transition-all duration-300'
                            style={{ width: `${((currentPromptIndex + 1) / prompts.length) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
