import React, { useState, useRef } from 'react'
import browser from 'webextension-polyfill'

export default () => {
  const [fact, setFact] = useState('Click the button to fetch a fact!')
  const [loading, setLoading] = useState(false)
  const [uploadedContent, setUploadedContent] = useState('')
  const [fileName, setFileName] = useState('')
  const fileInputRef = useRef<HTMLInputElement>(null)

  async function handleOnClick() {
    setLoading(true)
    const {data} = await browser.runtime.sendMessage({ action: 'fetch' })
    setFact(data)
    setLoading(false)
  }

  function handleFileUpload(event: React.ChangeEvent<HTMLInputElement>) {
    const file = event.target.files?.[0]

    if (!file) {
      return
    }

    // Check if file is a .txt file
    if (!file.name.toLowerCase().endsWith('.txt')) {
      alert('Please select a .txt file only')
      return
    }

    const reader = new FileReader()

    reader.onload = (e) => {
      const content = e.target?.result as string
      setUploadedContent(content)
      setFileName(file.name)
    }

    reader.onerror = () => {
      alert('Error reading file')
    }

    reader.readAsText(file)
  }

  function handleUploadClick() {
    fileInputRef.current?.click()
  }

  function clearUploadedFile() {
    setUploadedContent('')
    setFileName('')
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className='flex flex-col gap-4 p-4 shadow-sm bg-gradient-to-r from-purple-500 to-pink-500 w-96'>
      <h1>Cat Facts!</h1>

      <button
        className='px-4 py-2 font-semibold text-sm bg-cyan-500 text-white rounded-full shadow-sm disabled:opacity-75 w-48'
        disabled={loading} onClick={handleOnClick}>Get a Cat Fact!
      </button>

      <p className='text-white'>{fact}</p>

      {/* File Upload Section */}
      <div className='border-t border-white/20 pt-4'>
        <h2 className='text-white font-semibold mb-2'>Upload Text File</h2>

        <input
          ref={fileInputRef}
          type="file"
          accept=".txt"
          onChange={handleFileUpload}
          className="hidden"
        />

        <button
          onClick={handleUploadClick}
          className='px-4 py-2 font-semibold text-sm bg-green-500 text-white rounded-full shadow-sm hover:bg-green-600 transition-colors w-48'
        >
          Choose .txt File
        </button>

        {fileName && (
          <div className='mt-3 p-3 bg-white/10 rounded-lg'>
            <div className='flex justify-between items-center mb-2'>
              <span className='text-white text-sm font-medium'>
                📄 {fileName}
              </span>
              <button
                onClick={clearUploadedFile}
                className='text-red-300 hover:text-red-100 text-sm'
              >
                ✕
              </button>
            </div>

            <div className='max-h-32 overflow-y-auto bg-white/5 p-2 rounded text-white text-xs'>
              <pre className='whitespace-pre-wrap break-words'>
                {uploadedContent.length > 200
                  ? uploadedContent.substring(0, 200) + '...'
                  : uploadedContent
                }
              </pre>
            </div>

            <p className='text-white/70 text-xs mt-1'>
              {uploadedContent.length} characters
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
