import React, { useState, useRef, useEffect } from 'react'
import browser from 'webextension-polyfill'

export default () => {
  const [uploadedContent, setUploadedContent] = useState('')
  const [fileName, setFileName] = useState('')
  const [prompts, setPrompts] = useState<string[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentPromptIndex, setCurrentPromptIndex] = useState(0)
  const [processStatus, setProcessStatus] = useState('')
  const [delaySeconds, setDelaySeconds] = useState(3)
  const fileInputRef = useRef<HTMLInputElement>(null)

  function parsePrompts(content: string): string[] {
    // Split by double newlines or single newlines, filter out empty strings
    const lines = content.split(/\n+/).map(line => line.trim()).filter(line => line.length > 0)
    return lines
  }

  function handleFileUpload(event: React.ChangeEvent<HTMLInputElement>) {
    const file = event.target.files?.[0]

    if (!file) {
      return
    }

    // Check if file is a .txt file
    if (!file.name.toLowerCase().endsWith('.txt')) {
      alert('Please select a .txt file only')
      return
    }

    const reader = new FileReader()

    reader.onload = (e) => {
      const content = e.target?.result as string
      setUploadedContent(content)
      setFileName(file.name)

      // Parse prompts from the content
      const parsedPrompts = parsePrompts(content)
      setPrompts(parsedPrompts)
    }

    reader.onerror = () => {
      alert('Error reading file')
    }

    reader.readAsText(file)
  }

  function handleUploadClick() {
    fileInputRef.current?.click()
  }

  function clearUploadedFile() {
    setUploadedContent('')
    setFileName('')
    setPrompts([])
    setCurrentPromptIndex(0)
    setProcessStatus('')
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  async function checkRunwareTab(): Promise<boolean> {
    try {
      const tabs = await browser.tabs.query({ url: '*://my.runware.ai/*' })
      return tabs.length > 0
    } catch (error) {
      console.error('Error checking for Runware tab:', error)
      return false
    }
  }

  async function startProcessingPrompts() {
    if (prompts.length === 0) {
      alert('No prompts found in the uploaded file')
      return
    }

    const hasRunwareTab = await checkRunwareTab()
    if (!hasRunwareTab) {
      const shouldOpen = confirm(
        'Runware AI playground is not open. Would you like to open it now?\n\n' +
        'Please make sure to navigate to the playground page after opening.'
      )
      if (shouldOpen) {
        await browser.tabs.create({
          url: 'https://my.runware.ai/playground?modelAIR=runware%3A101%401&modelArchitecture=flux1d'
        })
      }
      return
    }

    setIsProcessing(true)
    setCurrentPromptIndex(0)
    setProcessStatus(`Starting to process ${prompts.length} prompts...`)

    try {
      // Send message to content script to start processing
      await browser.runtime.sendMessage({
        action: 'startPromptProcessing',
        prompts: prompts,
        delaySeconds: delaySeconds
      })
    } catch (error) {
      console.error('Error starting prompt processing:', error)
      setProcessStatus('Error: Failed to start processing')
      setIsProcessing(false)
    }
  }

  useEffect(() => {
    // Listen for processing updates from background script
    const messageListener = (message: any) => {
      if (message.action === 'processingUpdate') {
        setCurrentPromptIndex(message.currentIndex || 0)
        setProcessStatus(message.status || '')
      } else if (message.action === 'processingComplete') {
        setIsProcessing(false)
        setProcessStatus('All prompts processed successfully!')
      } else if (message.action === 'processingError') {
        setIsProcessing(false)
        setProcessStatus(`Error: ${message.error}`)
      }
    }

    // Check if browser runtime is available (not in test environment)
    if (browser?.runtime?.onMessage) {
      browser.runtime.onMessage.addListener(messageListener)

      return () => {
        browser.runtime.onMessage.removeListener(messageListener)
      }
    }
  }, [])

  return (
    <div className='flex flex-col gap-4 p-4 shadow-sm bg-gradient-to-r from-purple-500 to-pink-500 w-96'>
      <h1 className='text-white font-bold'>Prompt Processor</h1>

      {/* File Upload Section */}
      <div className='border-t border-white/20 pt-4'>
        <h2 className='text-white font-semibold mb-2'>Upload Text File</h2>

        <input
          ref={fileInputRef}
          type="file"
          accept=".txt"
          onChange={handleFileUpload}
          className="hidden"
        />

        <button
          onClick={handleUploadClick}
          className='px-4 py-2 font-semibold text-sm bg-green-500 text-white rounded-full shadow-sm hover:bg-green-600 transition-colors w-48'
        >
          Choose .txt File
        </button>

        {fileName && (
          <div className='mt-3 p-3 bg-white/10 rounded-lg'>
            <div className='flex justify-between items-center mb-2'>
              <span className='text-white text-sm font-medium'>
                📄 {fileName}
              </span>
              <button
                onClick={clearUploadedFile}
                className='text-red-300 hover:text-red-100 text-sm'
              >
                ✕
              </button>
            </div>

            <div className='max-h-32 overflow-y-auto bg-white/5 p-2 rounded text-white text-xs'>
              <pre className='whitespace-pre-wrap break-words'>
                {uploadedContent.length > 200
                  ? uploadedContent.substring(0, 200) + '...'
                  : uploadedContent
                }
              </pre>
            </div>

            <div className='flex justify-between items-center mt-2'>
              <p className='text-white/70 text-xs'>
                {uploadedContent.length} characters • {prompts.length} prompts
              </p>
            </div>

            {prompts.length > 0 && (
              <div className='mt-3'>
                <div className='mb-3'>
                  <label className='block text-white text-xs font-medium mb-1'>
                    Delay between prompts (seconds):
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="60"
                    value={delaySeconds}
                    onChange={(e) => setDelaySeconds(parseInt(e.target.value) || 3)}
                    className='w-full px-2 py-1 text-sm rounded border border-white/20 bg-white/10 text-white placeholder-white/50'
                    placeholder="3"
                  />
                </div>

                <button
                  onClick={startProcessingPrompts}
                  disabled={isProcessing}
                  className='px-4 py-2 font-semibold text-sm bg-blue-500 text-white rounded-full shadow-sm hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed w-full'
                >
                  {isProcessing ? 'Processing...' : `Process ${prompts.length} Prompts`}
                </button>

                {processStatus && (
                  <div className='mt-2 p-2 bg-white/5 rounded text-white text-xs'>
                    {processStatus}
                    {isProcessing && (
                      <div className='mt-1'>
                        Progress: {currentPromptIndex + 1} / {prompts.length}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
