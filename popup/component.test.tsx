import React from 'react'
import { $, expect } from '@wdio/globals'
import { render } from '@testing-library/react'
import browser from 'webextension-polyfill'

import Component from './component.js'

describe('Popup Component Tests', () => {
  it('should render prompt processor popup', async () => {
    render(<Component />)
    await expect($('h1')).toHaveText('Prompt Processor')
  })

  it('should display file upload section', async () => {
    render(<Component />)

    // Check if upload section is present
    await expect($('h2=Upload Text File')).toBeDisplayed()

    // Check if upload button is present
    const uploadBtn = await $('aria/Choose .txt File')
    await expect(uploadBtn).toBeDisplayed()
  })
})
