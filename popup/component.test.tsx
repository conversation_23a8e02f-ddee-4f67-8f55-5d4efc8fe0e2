import React from 'react'
import { $, expect } from '@wdio/globals'
import { render } from '@testing-library/react'
import browser from 'webextension-polyfill'

import Component from './component.js'

describe('Popup Component Tests', () => {
  it('should render prompt processor popup', async () => {
    render(<Component />)
    await expect($('h1')).toHaveText('AI Prompt Processor')
  })

  it('should display file upload section', async () => {
    render(<Component />)

    // Check if upload section is present with new UI text
    await expect($('h3=Upload your prompts')).toBeDisplayed()

    // Check if upload button is present with new text
    const uploadBtn = await $('button=Choose File')
    await expect(uploadBtn).toBeDisplayed()
  })
})
