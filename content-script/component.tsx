import { useState, useEffect } from 'react'
import browser from 'webextension-polyfill'

export default () => {
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentPrompt, setCurrentPrompt] = useState('')
  const [processStatus, setProcessStatus] = useState('')

  function findTextarea(): HTMLTextAreaElement | null {
    // Look for the specific textarea with the placeholder text
    const textareas = document.querySelectorAll('textarea')
    for (const textarea of textareas) {
      if (textarea.placeholder.includes('Type your prompt here to start')) {
        return textarea
      }
    }
    return null
  }

  function simulateKeyPress(element: HTMLElement, key: string, ctrlKey: boolean = false) {
    const event = new KeyboardEvent('keydown', {
      key: key,
      code: key === 'Enter' ? 'Enter' : key,
      ctrlKey: ctrlKey,
      bubbles: true,
      cancelable: true
    })
    element.dispatchEvent(event)
  }

  async function processPrompts(prompts: string[], delaySeconds: number = 3) {
    setIsProcessing(true)
    setProcessStatus('Starting prompt processing...')

    for (let i = 0; i < prompts.length; i++) {
      const prompt = prompts[i].trim()
      if (!prompt) continue

      setCurrentPrompt(prompt)
      setProcessStatus(`Processing prompt ${i + 1} of ${prompts.length}`)

      try {
        const textarea = findTextarea()
        if (!textarea) {
          setProcessStatus('Error: Could not find textarea on page')
          break
        }

        // Clear existing content
        textarea.value = ''
        textarea.dispatchEvent(new Event('input', { bubbles: true }))

        // Set the prompt text
        textarea.value = prompt
        textarea.dispatchEvent(new Event('input', { bubbles: true }))

        // Wait a moment for the UI to update
        await new Promise(resolve => setTimeout(resolve, 1000))

        // Simulate Ctrl+Enter to submit
        simulateKeyPress(textarea, 'Enter', true)

        // Wait before processing next prompt using the specified delay
        if (i < prompts.length - 1) { // Don't wait after the last prompt
          await new Promise(resolve => setTimeout(resolve, delaySeconds * 1000))
        }

      } catch (error) {
        console.error('Error processing prompt:', error)
        setProcessStatus(`Error processing prompt ${i + 1}`)
        break
      }
    }

    setIsProcessing(false)
    setProcessStatus('Processing completed!')
    setCurrentPrompt('')
  }

  useEffect(() => {
    // Listen for messages from background script
    const messageListener = (message: any) => {
      if (message.action === 'processPrompts') {
        processPrompts(message.prompts, message.delaySeconds || 3)
      }
    }

    // Check if browser runtime is available (not in test environment)
    if (browser?.runtime?.onMessage) {
      browser.runtime.onMessage.addListener(messageListener)

      return () => {
        browser.runtime.onMessage.removeListener(messageListener)
      }
    }
  }, [])

  return (
    <div className='fixed top-4 right-4 z-[10000]'>
      <div className='flex flex-col gap-4 p-4 shadow-lg bg-gradient-to-r from-purple-500 to-pink-500 w-80 rounded-md'>
        <h1 className='text-white font-bold'>Prompt Processor</h1>

        {isProcessing && (
          <div className='bg-white/10 p-3 rounded'>
            <div className='text-white text-sm font-medium mb-1'>
              {processStatus}
            </div>
            {currentPrompt && (
              <div className='text-white/80 text-xs max-h-20 overflow-y-auto'>
                Current: {currentPrompt.substring(0, 100)}
                {currentPrompt.length > 100 ? '...' : ''}
              </div>
            )}
          </div>
        )}

        {!isProcessing && (
          <div className='text-white text-sm text-center'>
            Ready to process prompts from uploaded file
          </div>
        )}
      </div>
    </div>
  )
}
