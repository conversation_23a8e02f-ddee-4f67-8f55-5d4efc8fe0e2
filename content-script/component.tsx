import { useState, useEffect } from 'react'
import browser from 'webextension-polyfill'

export default () => {
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentPrompt, setCurrentPrompt] = useState('')
  const [processStatus, setProcessStatus] = useState('')

  function findTextarea(): HTMLTextAreaElement | null {
    console.log('🔍 Looking for textarea...')

    // Method 1: Look for the specific textarea with the placeholder text
    const textareas = document.querySelectorAll('textarea')
    console.log(`📊 Found ${textareas.length} textareas on page`)

    for (const textarea of textareas) {
      console.log(`📝 Textarea placeholder: "${textarea.placeholder}"`)
      if (textarea.placeholder.includes('Type your prompt here to start')) {
        console.log('✅ Found target textarea by placeholder!')
        return textarea
      }
    }

    // Method 2: Look for textarea with specific classes
    const classBasedTextarea = document.querySelector('textarea.w-full.bg-transparent.outline-none') as HTMLTextAreaElement
    if (classBasedTextarea) {
      console.log('✅ Found target textarea by classes!')
      return classBasedTextarea
    }

    // Method 3: Look for any textarea in the main content area
    const anyTextarea = document.querySelector('textarea') as HTMLTextAreaElement
    if (anyTextarea) {
      console.log('⚠️ Using first available textarea as fallback')
      return anyTextarea
    }

    console.log('❌ No textarea found!')
    return null
  }

  function simulateKeyPress(element: HTMLElement, key: string, ctrlKey: boolean = false) {
    const event = new KeyboardEvent('keydown', {
      key: key,
      code: key === 'Enter' ? 'Enter' : key,
      ctrlKey: ctrlKey,
      bubbles: true,
      cancelable: true
    })
    element.dispatchEvent(event)
  }

  async function processPrompts(prompts: string[], delaySeconds: number = 3) {
    console.log('🚀 Content Script: Starting prompt processing!')
    console.log(`📝 Prompts to process: ${prompts.length}`)
    console.log(`⏱️ Delay between prompts: ${delaySeconds}s`)

    setIsProcessing(true)
    setProcessStatus('🔍 Looking for textarea...')

    for (let i = 0; i < prompts.length; i++) {
      const prompt = prompts[i].trim()
      if (!prompt) {
        console.log(`⏭️ Skipping empty prompt at index ${i}`)
        continue
      }

      console.log(`📝 Processing prompt ${i + 1}/${prompts.length}: "${prompt.substring(0, 50)}..."`)
      setCurrentPrompt(prompt)
      setProcessStatus(`🔄 Processing prompt ${i + 1} of ${prompts.length}`)

      try {
        const textarea = findTextarea()
        if (!textarea) {
          console.log('❌ Could not find textarea!')
          setProcessStatus('❌ Error: Could not find textarea on page')
          break
        }

        console.log('✅ Textarea found, clearing content...')
        // Clear existing content
        textarea.value = ''
        textarea.dispatchEvent(new Event('input', { bubbles: true }))
        textarea.dispatchEvent(new Event('change', { bubbles: true }))

        console.log('📝 Setting new prompt text...')
        // Set the prompt text
        textarea.value = prompt
        textarea.dispatchEvent(new Event('input', { bubbles: true }))
        textarea.dispatchEvent(new Event('change', { bubbles: true }))

        // Focus the textarea
        textarea.focus()

        console.log('⏳ Waiting 1 second for UI to update...')
        // Wait a moment for the UI to update
        await new Promise(resolve => setTimeout(resolve, 1000))

        console.log('⌨️ Simulating Ctrl+Enter...')
        // Simulate Ctrl+Enter to submit
        simulateKeyPress(textarea, 'Enter', true)

        // Also try alternative submission methods
        const enterEvent = new KeyboardEvent('keydown', {
          key: 'Enter',
          code: 'Enter',
          ctrlKey: true,
          bubbles: true,
          cancelable: true
        })
        textarea.dispatchEvent(enterEvent)

        console.log(`✅ Prompt ${i + 1} submitted successfully`)

        // Wait before processing next prompt using the specified delay
        if (i < prompts.length - 1) { // Don't wait after the last prompt
          console.log(`⏳ Waiting ${delaySeconds} seconds before next prompt...`)
          setProcessStatus(`⏳ Waiting ${delaySeconds}s before next prompt...`)
          await new Promise(resolve => setTimeout(resolve, delaySeconds * 1000))
        }

      } catch (error) {
        console.error('❌ Error processing prompt:', error)
        setProcessStatus(`❌ Error processing prompt ${i + 1}: ${error.message}`)
        break
      }
    }

    console.log('🎉 All prompts processed!')
    setIsProcessing(false)
    setProcessStatus('🎉 Processing completed!')
    setCurrentPrompt('')
  }

  useEffect(() => {
    console.log('🎯 Content Script: Setting up message listener...')

    // Listen for messages from background script
    const messageListener = (message: any) => {
      console.log('📨 Content Script: Received message:', message)

      if (message.action === 'processPrompts') {
        console.log('✅ Content Script: Processing prompts message received!')
        console.log(`📝 Prompts: ${message.prompts?.length || 0}`)
        console.log(`⏱️ Delay: ${message.delaySeconds || 3}s`)
        processPrompts(message.prompts, message.delaySeconds || 3)
      } else {
        console.log('❓ Content Script: Unknown message action:', message.action)
      }
    }

    // Check if browser runtime is available (not in test environment)
    if (browser?.runtime?.onMessage) {
      console.log('✅ Content Script: Message listener registered')
      browser.runtime.onMessage.addListener(messageListener)

      return () => {
        console.log('🧹 Content Script: Cleaning up message listener')
        browser.runtime.onMessage.removeListener(messageListener)
      }
    } else {
      console.log('❌ Content Script: Browser runtime not available')
    }
  }, [])

  return (
    <div className='fixed top-4 right-4 z-[10000]'>
      <div className='flex flex-col gap-4 p-4 shadow-lg bg-gradient-to-r from-purple-500 to-pink-500 w-80 rounded-md'>
        <h1 className='text-white font-bold'>Prompt Processor</h1>

        {isProcessing && (
          <div className='bg-white/10 p-3 rounded'>
            <div className='text-white text-sm font-medium mb-1'>
              {processStatus}
            </div>
            {currentPrompt && (
              <div className='text-white/80 text-xs max-h-20 overflow-y-auto'>
                Current: {currentPrompt.substring(0, 100)}
                {currentPrompt.length > 100 ? '...' : ''}
              </div>
            )}
          </div>
        )}

        {!isProcessing && (
          <div className='text-white text-sm text-center'>
            Ready to process prompts from uploaded file
          </div>
        )}
      </div>
    </div>
  )
}
