import { $, expect } from '@wdio/globals'
import { render } from '@testing-library/react'

import Component from './component.js'

describe('Content Script Component Tests', () => {
  it('should render prompt processor component', async () => {
    render(<Component />)
    await expect($('h1')).toHaveText('Prompt Processor')

    // Check that the ready message is displayed when not processing
    await expect($('div*=Ready to process prompts')).toBeDisplayed()
  })
})
