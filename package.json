{"name": "my-web-extension", "version": "0.0.0", "description": "A web extension that works in all browser.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "type": "module", "private": true, "scripts": {"clean": "rimraf dist ./*.zip ./*.crx ./*.pem", "dev": "vite", "build:firefox": "NODE_ENV=production run-s clean compile build:firefox:*", "build:firefox:manifest": "mv dist/manifestv2.json dist/manifest.json", "build:chrome": "NODE_ENV=production run-s clean compile build:chrome:*", "build:chrome:script": "vite build --config vite.chrome.config.ts", "build:chrome:manifest": "rm dist/manifestv2.json", "bundle": "run-s bundle:*", "bundle:clean": "run-s clean", "bundle:firefox": "run-s bundle:firefox:*", "bundle:firefox:build": "run-s build:firefox", "bundle:firefox:zip": "web-ext build -s dist/ -a . && mv ./my_web_extension-$npm_package_version.zip ./web-extension-firefox-v$npm_package_version.xpi", "bundle:chrome": "run-s bundle:chrome:*", "bundle:chrome:build": "run-s build:chrome", "bundle:chrome:zip": "zip -r ./web-extension-chrome-v$npm_package_version.zip ./dist", "bundle:chrome:crx": "crx pack ./dist -o ./web-extension-chrome-v$npm_package_version.crx", "compile": "run-s compile:*", "compile:tsc": "tsc", "compile:vite": "run-p compile:vite:*", "compile:vite:web": "vite build", "compile:vite:js": "vite build --config vite.content.config.ts", "preview": "vite preview", "start": "run-s start:chrome", "start:chrome": "run-p start:chrome:*", "start:chrome:watcher": "npm run watch:chrome", "start:chrome:startBrowser": "./.github/scripts/start.js chrome", "start:firefox": "npm-run-all bundle:firefox --parallel start:firefox:*", "start:firefox:watcher": "npm run watch:firefox", "start:firefox:startBrowser": "./.github/scripts/start.js firefox", "test": "run-s test:*", "test:component": "wdio run ./wdio.ui.conf.ts", "test:e2e": "wdio run ./wdio.e2e.conf.ts", "version": "run-s version:*", "version:update": "npx replace ' \"version\": \"(\\d+).(\\d+).(\\d+)\"' \" \\\"version\\\": \\\"$npm_package_version\\\"\" ./public/*.json", "version:bundle": "run-s bundle", "version:git": "git add public", "watch:chrome": "run-p watch:all:* watch:chrome:script", "watch:firefox": "run-p watch:chrome:*", "watch:all:web": "npm run compile:vite:web -- --watch", "watch:all:js": "npm run compile:vite:js -- --watch", "watch:chrome:script": "sleep 1 && npm run build:chrome:script -- --watch"}, "devDependencies": {"@testing-library/react": "^14.0.0", "@types/node": "^20.5.0", "@types/react": "^18.2.20", "@types/webextension-polyfill": "^0.10.1", "@vitejs/plugin-react": "^4.0.4", "@wdio/browser-runner": "^8.14.6", "@wdio/cli": "^8.14.6", "@wdio/firefox-profile-service": "^8.14.0", "@wdio/mocha-framework": "^8.14.0", "@wdio/spec-reporter": "^8.14.0", "autoprefixer": "^10.4.15", "chrome-launcher": "^1.0.0", "crx": "^5.0.1", "npm-run-all": "^4.1.5", "postcss": "^8.4.27", "release-it": "^17.0.0", "replace": "^1.2.2", "rimraf": "^5.0.1", "tailwindcss": "^3.3.3", "ts-node": "^10.9.1", "typescript": "^5.1.6", "vite": "^5.0.2", "web-ext": "^7.6.2", "webextension-polyfill": "^0.10.0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}}