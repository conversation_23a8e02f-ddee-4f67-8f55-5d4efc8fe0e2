name: 'Test'
on: # rebuild any PRs and main branch changes
  pull_request:
  push:
    branches:
      - main

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Check out Git repository
        uses: actions/checkout@v2
      - name: Install
        run: npm ci
      - name: Bundle
        run: npm run bundle
      - name: Test
        run: npm run test
      - uses: actions/upload-artifact@v3
        with:
          name: extension-files
          path: |
            ./*.crx
            ./*.zip
